{"createdAt": "2024-02-23T16:58:31.616Z", "updatedAt": "2024-02-23T16:58:31.616Z", "id": "srOnR8PAY3u4RSwb", "name": "Demo workflow", "active": false, "nodes": [{"parameters": {}, "id": "74003dcd-2ac7-4caa-a1cd-adecc5143c07", "name": "<PERSON><PERSON>", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1, "position": [660, 340], "webhookId": "cdb5c076-d458-4b9d-8398-f43bd25059b1"}, {"parameters": {}, "id": "ce8c3da4-899c-4cc4-af73-8096c64eec64", "name": "Basic LLM Chain", "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.3, "position": [880, 340]}, {"parameters": {"model": "llama3.2:latest", "options": {}}, "id": "3dee878b-d748-4829-ac0a-cfd6705d31e5", "name": "<PERSON><PERSON><PERSON> Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOllama", "typeVersion": 1, "position": [900, 560], "credentials": {"ollamaApi": {"id": "xHuYe0MDGOs9IpBW", "name": "Local Ollama service"}}}], "connections": {"Chat Trigger": {"main": [[{"node": "Basic LLM Chain", "type": "main", "index": 0}]]}, "Ollama Chat Model": {"ai_languageModel": [[{"node": "Basic LLM Chain", "type": "ai_languageModel", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "versionId": "4e2affe6-bb1c-4ddc-92f9-dde0b7656796", "triggerCount": 0, "tags": []}